# Blood Pressure Metrics - Cardiowell R Analytics System

## Total Metrics Count: **67 Distinct Metrics**

---

## 1. Basic Statistical Metrics (15 metrics)

### Primary Measurements
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **Mean SBP** | Average systolic blood pressure | `mean(sbp_readings)` |
| **Mean DBP** | Average diastolic blood pressure | `mean(dbp_readings)` |
| **Mean Pulse** | Average pulse rate | `mean(pulse_readings)` |
| **MAP** | Mean arterial pressure | `DBP + (SBP - DBP) / 3` |

### Variability Metrics
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **SBP Standard Deviation** | Variability in systolic readings | `sd(sbp_readings)` |
| **DBP Standard Deviation** | Variability in diastolic readings | `sd(dbp_readings)` |
| **Pulse Standard Deviation** | Variability in pulse readings | `sd(pulse_readings)` |
| **Pulse Pressure SD** | Variability in pulse pressure | `sd(sbp - dbp)` |

### Coefficient of Variation
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **CV SBP** | Coefficient of variation for SBP | `(SBP_SD / Mean_SBP) × 100` |
| **CV DBP** | Coefficient of variation for DBP | `(DBP_SD / Mean_DBP) × 100` |
| **CV Pulse** | Coefficient of variation for pulse | `(Pulse_SD / Mean_Pulse) × 100` |

### Range Statistics
| Metric | Description |
|--------|-------------|
| **SBP Min** | Minimum systolic reading |
| **SBP Max** | Maximum systolic reading |
| **SBP Range** | Difference between max and min SBP |
| **DBP Min** | Minimum diastolic reading |
| **DBP Max** | Maximum diastolic reading |
| **DBP Range** | Difference between max and min DBP |
| **Pulse Min** | Minimum pulse reading |
| **Pulse Max** | Maximum pulse reading |
| **Pulse Range** | Difference between max and min pulse |

---

## 2. Blood Pressure Classification Metrics (9 metrics)

### Hypertension Stage Classifications
| Stage | SBP Range | DBP Range | Description |
|-------|-----------|-----------|-------------|
| **Normal** | < 120 | < 80 | Optimal blood pressure |
| **Elevated** | 120-129 | < 80 | Pre-hypertension |
| **Stage 1 Hypertension** | 130-139 | 80-89 | Mild hypertension |
| **Stage 2 Hypertension** | ≥ 140 | ≥ 90 | Moderate hypertension |
| **Hypertensive Crisis** | ≥ 180 | ≥ 120 | Severe hypertension |
| **Hypotensive** | < 90 | < 60 | Low blood pressure |

### Control Metrics
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **TIR%** | Time in Range percentage | `(readings_in_range / total_readings) × 100` |
| **TAR%** | Time Above Range percentage | `(readings_above_range / total_readings) × 100` |
| **Systolic Load%** | Percentage with SBP ≥ 130 | `(readings_sbp_≥130 / total_readings) × 100` |
| **Diastolic Load%** | Percentage with DBP ≥ 80 | `(readings_dbp_≥80 / total_readings) × 100` |

---

## 3. Circadian Rhythm Metrics (18 metrics)

### Time-of-Day Analysis
| Time Period | Hours | Metrics |
|-------------|-------|---------|
| **Morning** | 6 AM - 12 PM | Mean SBP, Mean DBP |
| **Afternoon** | 12 PM - 6 PM | Mean SBP, Mean DBP |
| **Evening** | 6 PM - 10 PM | Mean SBP, Mean DBP |
| **Night** | 10 PM - 6 AM | Mean SBP, Mean DBP |

### Circadian Patterns
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **Morning Surge** | Morning SBP - Night Min SBP | `morning_mean_sbp - night_min_sbp` |
| **Dipping Percentage** | Day-night BP difference | `(day_mean - night_mean) / day_mean × 100` |
| **Dipping Classification** | Categorization of dipping pattern | See classification table below |
| **Morning Hypertension** | Morning BP elevation | `morning_sbp ≥ 135 OR morning_dbp ≥ 85` |

### Dipping Classification
| Classification | Dipping Percentage | Description |
|----------------|-------------------|-------------|
| **Reverse Dipper** | < 0% | Night BP higher than day BP |
| **Non-Dipper** | 0-10% | Minimal day-night difference |
| **Normal Dipper** | 10-20% | Healthy circadian pattern |
| **Extreme Dipper** | > 20% | Excessive day-night difference |

### Nocturnal Hypertension
| Metric | Description | Criteria |
|---------|-------------|----------|
| **Absolute Nocturnal HTN** | Night hypertension | `night_sbp ≥ 120 OR night_dbp ≥ 70` |
| **Relative Nocturnal HTN** | Relative night elevation | `dipping_percentage < 10%` |

---

## 4. Advanced Variability Metrics (8 metrics)

### Trend Analysis
| Metric | Description | Method |
|--------|-------------|--------|
| **Overall Trend Slope SBP** | Long-term SBP trend | Linear regression over all readings |
| **Overall Trend Slope DBP** | Long-term DBP trend | Linear regression over all readings |
| **30-Day Trend Slope SBP** | Recent SBP trend | Linear regression over last 30 days |
| **30-Day Trend Slope DBP** | Recent DBP trend | Linear regression over last 30 days |

### Variability Measures
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **DDV SBP** | Day-to-day variability SBP | `sd(daily_mean_sbp)` |
| **DDV DBP** | Day-to-day variability DBP | `sd(daily_mean_dbp)` |
| **ARV SBP** | Average real variability SBP | `mean(abs(diff(sbp_readings)))` |
| **ARV DBP** | Average real variability DBP | `mean(abs(diff(dbp_readings)))` |

---

## 5. Pulse Rate Analysis (7 metrics)

### Bradycardia Detection
| Threshold | Description | Count |
|-----------|-------------|-------|
| **Pulse < 40** | Severe bradycardia | Count of readings |
| **Pulse < 50** | Moderate bradycardia | Count of readings |
| **Pulse < 60** | Mild bradycardia | Count of readings |

### Tachycardia Detection
| Threshold | Description | Count |
|-----------|-------------|-------|
| **Pulse > 100** | Mild tachycardia | Count of readings |
| **Pulse > 120** | Moderate tachycardia | Count of readings |
| **Pulse > 150** | Severe tachycardia | Count of readings |

### Special Conditions
| Metric | Description | Criteria |
|---------|-------------|----------|
| **High BP + Low Pulse Count** | Elevated BP with bradycardia | `(SBP ≥ 130 AND DBP ≥ 80) AND pulse < 60` |

---

## 6. Temporal Analysis Metrics (6 metrics)

### Weekend vs Weekday Patterns
| Metric | Description |
|--------|-------------|
| **Weekend Mean SBP** | Average SBP on weekends (Sat-Sun) |
| **Weekend Mean DBP** | Average DBP on weekends (Sat-Sun) |
| **Weekday Mean SBP** | Average SBP on weekdays (Mon-Fri) |
| **Weekday Mean DBP** | Average DBP on weekdays (Mon-Fri) |
| **Weekend-Weekday Delta SBP** | Difference between weekend and weekday SBP |
| **Weekend-Weekday Delta DBP** | Difference between weekend and weekday DBP |

---

## 7. Data Quality & Context Metrics (4 metrics)

### Reading Statistics
| Metric | Description |
|--------|-------------|
| **Total Readings Count** | Total number of valid readings |
| **Days with Readings** | Number of unique days with readings |
| **First Reading Timestamp** | Timestamp of earliest reading |
| **Last Reading Timestamp** | Timestamp of most recent reading |

---

## 8. Additional Derived Metrics

### Moving Averages
| Metric | Description |
|--------|-------------|
| **7-Day Moving Average SBP** | Rolling 7-day average of SBP |
| **7-Day Moving Average DBP** | Rolling 7-day average of DBP |

### Latest Reading Details
| Metric | Description |
|--------|-------------|
| **Latest SBP** | Most recent systolic reading |
| **Latest DBP** | Most recent diastolic reading |
| **Latest MAP** | Most recent mean arterial pressure |
| **Latest Pulse** | Most recent pulse reading |
| **Latest Classification** | Classification of most recent reading |

---

## Summary

**Total: 67 Blood Pressure Metrics**

1. **Basic Statistical Metrics** (15)
2. **Classification Metrics** (9)  
3. **Circadian Rhythm Metrics** (18)
4. **Advanced Variability Metrics** (8)
5. **Pulse Rate Analysis** (7)
6. **Temporal Analysis** (6)
7. **Data Quality Metrics** (4)
